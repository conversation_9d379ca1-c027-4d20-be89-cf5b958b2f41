from agents.sp_parser_agent.agent import parse_stored_procedure
from agents.file_validation_agent.agent import validate_row
from agents.data_validation_agent.agent import validate_business_logic
from agents.guardrail_agent.agent import enforce_guardrails
from utils.file_handler import load_sql_file, load_excel_as_dicts, save_output_excel
from utils.json_schema_utils import validate_rules_schema
from memory.session_context import session_context


def run_validation_chain(sql_file_path: str, excel_path: str):
    sql_text = load_sql_file(sql_file_path)
    procedure_name = sql_file_path.split("/")[-1].replace(".sql", "")
    session_context.set("procedure_name", procedure_name)

    # Step 1: Extract rules from SP
    rule_result = parse_stored_procedure(sql_text, procedure_name)
    rules_json = rule_result.dict()

    # Step 2: Validate rules structure
    validate_rules_schema(rules_json)

    # Step 3: Load Excel
    rows = load_excel_as_dicts(excel_path)

    validated_rows = []
    for row in rows:
        val_res = validate_row(row, rules_json)
        biz_res = validate_business_logic(row, rules_json, {}, {})

        row["error"] = val_res.error or not biz_res.valid
        row["error_message"] = val_res.error_message if val_res.error else biz_res.reason
        validated_rows.append(row)

    # Step 4: Enforce guardrails on output
    final_output = {
        "procedure_name": procedure_name,
        "rules": rules_json.get("rules", []),
        "validated_sample": validated_rows[:5]  # partial sample
    }
    guardrail_result = enforce_guardrails(final_output, {"columns": list(rows[0].keys())}, rules_json)

    # Step 5: Save output
    output_path = save_output_excel(validated_rows, procedure_name)
    return {
        "status": guardrail_result.status,
        "violations": guardrail_result.violations,
        "file": output_path
    }
