import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import yaml
from datetime import datetime

class AgentLoggingConfig:
    """
    Comprehensive logging configuration for AI validation agents.
    Provides detailed agent-wise logging with complete error reports.
    """
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        self.config = self._load_config(config_path)
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # Initialize logging
        self._setup_logging()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load logging configuration from settings file."""
        try:
            with open(config_path, 'r') as file:
                config = yaml.safe_load(file)
                return config.get('logging', {})
        except FileNotFoundError:
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Return default logging configuration."""
        return {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_path': 'logs/validation_agent.log',
            'max_file_size_mb': 10,
            'backup_count': 5,
            'agent_specific_logging': True
        }
    
    def _setup_logging(self):
        """Setup comprehensive logging configuration."""
        
        # Get logging configuration
        log_level = getattr(logging, self.config.get('level', 'INFO').upper())
        log_format = self.config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        # Create formatter
        formatter = logging.Formatter(log_format)
        
        # Setup root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # Main log file handler
        main_log_file = self.log_dir / "validation_agent.log"
        file_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=self.config.get('max_file_size_mb', 10) * 1024 * 1024,
            backupCount=self.config.get('backup_count', 5)
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # Agent-specific logging
        if self.config.get('agent_specific_logging', True):
            self._setup_agent_specific_logging(formatter, log_level)
        
        # Error-only log file
        error_log_file = self.log_dir / "errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=self.config.get('max_file_size_mb', 10) * 1024 * 1024,
            backupCount=self.config.get('backup_count', 5)
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
        
        logging.info("Comprehensive logging system initialized")
    
    def _setup_agent_specific_logging(self, formatter, log_level):
        """Setup individual log files for each agent."""
        
        agents = [
            'sp_parser_agent',
            'file_validation_agent', 
            'data_validation_agent',
            'guardrail_agent'
        ]
        
        for agent_name in agents:
            # Create agent-specific logger
            agent_logger = logging.getLogger(f'agents.{agent_name}')
            agent_logger.setLevel(log_level)
            
            # Agent-specific file handler
            agent_log_file = self.log_dir / f"{agent_name}.log"
            agent_handler = logging.handlers.RotatingFileHandler(
                agent_log_file,
                maxBytes=self.config.get('max_file_size_mb', 10) * 1024 * 1024,
                backupCount=self.config.get('backup_count', 5)
            )
            agent_handler.setLevel(log_level)
            agent_handler.setFormatter(formatter)
            agent_logger.addHandler(agent_handler)
            
            # Prevent duplicate logs in root logger
            agent_logger.propagate = False
            
            logging.info(f"Agent-specific logging configured for {agent_name}")

class DetailedErrorReporter:
    """
    Provides detailed error reporting with complete context information.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def report_agent_error(self, agent_name: str, error: Exception, context: Dict[str, Any]):
        """
        Report a detailed agent error with full context.
        
        Args:
            agent_name: Name of the agent where error occurred
            error: The exception that occurred
            context: Additional context information
        """
        
        error_report = {
            'timestamp': datetime.now().isoformat(),
            'agent_name': agent_name,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'stack_trace': self._get_stack_trace()
        }
        
        # Log the detailed error
        self.logger.error(f"DETAILED ERROR REPORT for {agent_name}")
        self.logger.error(f"Error Type: {error_report['error_type']}")
        self.logger.error(f"Error Message: {error_report['error_message']}")
        self.logger.error(f"Context: {error_report['context']}")
        self.logger.error(f"Stack Trace: {error_report['stack_trace']}")
        
        return error_report
    
    def report_validation_step_error(self, step_name: str, input_data: Any, 
                                   error: Exception, additional_context: Optional[Dict] = None):
        """
        Report errors that occur during specific validation steps.
        
        Args:
            step_name: Name of the validation step
            input_data: Input data that caused the error
            error: The exception that occurred
            additional_context: Additional context information
        """
        
        context = {
            'validation_step': step_name,
            'input_data_type': type(input_data).__name__,
            'input_data_size': len(str(input_data)) if input_data else 0,
            'additional_context': additional_context or {}
        }
        
        # Sanitize input data for logging (remove sensitive information)
        sanitized_input = self._sanitize_for_logging(input_data)
        context['sanitized_input_preview'] = str(sanitized_input)[:500]
        
        return self.report_agent_error(f"validation_step_{step_name}", error, context)
    
    def _get_stack_trace(self) -> str:
        """Get the current stack trace as a string."""
        import traceback
        return traceback.format_exc()
    
    def _sanitize_for_logging(self, data: Any) -> Any:
        """
        Sanitize data for logging by removing sensitive information.
        
        Args:
            data: Data to sanitize
            
        Returns:
            Sanitized data safe for logging
        """
        if isinstance(data, dict):
            sanitized = {}
            sensitive_keys = ['password', 'token', 'key', 'secret', 'api_key']
            
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in sensitive_keys):
                    sanitized[key] = '[REDACTED]'
                elif isinstance(value, (dict, list)):
                    sanitized[key] = self._sanitize_for_logging(value)
                else:
                    sanitized[key] = value
            
            return sanitized
        
        elif isinstance(data, list):
            return [self._sanitize_for_logging(item) for item in data[:10]]  # Limit list size
        
        else:
            return data

# Global instances
_logging_config = None
_error_reporter = None

def setup_logging(config_path: str = "config/settings.yaml"):
    """Setup the global logging configuration."""
    global _logging_config
    _logging_config = AgentLoggingConfig(config_path)
    return _logging_config

def get_error_reporter() -> DetailedErrorReporter:
    """Get or create the global error reporter instance."""
    global _error_reporter
    if _error_reporter is None:
        _error_reporter = DetailedErrorReporter()
    return _error_reporter

def log_agent_activity(agent_name: str, activity: str, details: Dict[str, Any], 
                      status: str = "success"):
    """
    Log agent activity with detailed information.
    
    Args:
        agent_name: Name of the agent
        activity: Description of the activity
        details: Activity details
        status: Activity status (success, error, warning)
    """
    logger = logging.getLogger(f'agents.{agent_name}')
    
    log_message = f"ACTIVITY: {activity} - STATUS: {status.upper()}"
    
    if status == "success":
        logger.info(log_message)
        logger.info(f"Details: {details}")
    elif status == "error":
        logger.error(log_message)
        logger.error(f"Details: {details}")
    elif status == "warning":
        logger.warning(log_message)
        logger.warning(f"Details: {details}")
    else:
        logger.info(log_message)
        logger.info(f"Details: {details}")

# Initialize logging on module import
if not _logging_config:
    setup_logging()
