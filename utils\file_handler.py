import pandas as pd
from pathlib import Path

def load_sql_file(path: str) -> str:
    return Path(path).read_text(encoding='utf-8')

def load_excel_as_dicts(path: str) -> list:
    df = pd.read_excel(path)
    return df.to_dict(orient="records")

def save_output_excel(rows: list, procedure_name: str) -> str:
    df = pd.DataFrame(rows)
    out_path = f"data/output/{procedure_name}_validated.xlsx"
    Path("data/output").mkdir(parents=True, exist_ok=True)
    df.to_excel(out_path, index=False)
    return out_path