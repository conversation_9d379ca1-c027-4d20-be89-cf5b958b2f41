#!/usr/bin/env python3
"""
Startup script for CBRE Validation Agent
This script handles initialization and fixes common issues.
"""

import os
import sys
import logging
from pathlib import Path

def setup_environment():
    """Setup the environment for the validation agent."""
    
    print("🔧 Setting up CBRE Validation Agent environment...")
    
    # Create necessary directories
    directories = ["logs", "data", "data/input", "data/output", "data/temp"]
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ Directory: {directory}")
    
    # Setup basic logging first
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/validation_agent.log', encoding='utf-8')
        ]
    )
    print("   ✅ Basic logging configured")
    
    # Check Python version
    if sys.version_info < (3, 10):
        print(f"   ⚠️  Python {sys.version_info.major}.{sys.version_info.minor} detected. Python 3.10+ recommended.")
    else:
        print(f"   ✅ Python {sys.version_info.major}.{sys.version_info.minor} (compatible)")
    
    return True

def check_dependencies():
    """Check if all required dependencies are installed."""
    
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        'openai', 'langchain', 'langchain_openai', 'pandas', 'openpyxl', 
        'pydantic', 'sqlglot', 'streamlit', 'plotly', 'pyyaml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("   Run: pip install -r requirements.txt")
        return False
    
    print("   ✅ All dependencies installed")
    return True

def check_configuration():
    """Check if configuration files are properly set up."""
    
    print("\n⚙️  Checking configuration...")
    
    config_files = [
        "config/settings.yaml",
        "config/schema_definitions.json"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"   ✅ {config_file}")
            
            # Test loading the YAML file
            if config_file.endswith('.yaml'):
                try:
                    import yaml
                    with open(config_file, 'r', encoding='utf-8') as f:
                        yaml.safe_load(f)
                    print(f"   ✅ {config_file} (valid YAML)")
                except Exception as e:
                    print(f"   ❌ {config_file} (invalid YAML): {e}")
                    return False
        else:
            print(f"   ❌ {config_file} (missing)")
            return False
    
    return True

def check_api_key():
    """Check if OpenAI API key is configured."""
    
    print("\n🔑 Checking API configuration...")
    
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        if api_key.startswith('sk-'):
            print("   ✅ OpenAI API key configured")
            return True
        else:
            print("   ⚠️  OpenAI API key format looks incorrect")
            return False
    else:
        print("   ❌ OpenAI API key not set")
        print("   Set it with: set OPENAI_API_KEY=your-api-key")
        return False

def test_imports():
    """Test importing our modules."""
    
    print("\n🧪 Testing module imports...")
    
    modules_to_test = [
        'utils.openai_client',
        'utils.logging_config', 
        'utils.json_schema_utils',
        'utils.file_handler',
        'memory.session_context'
    ]
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except Exception as e:
            print(f"   ❌ {module}: {e}")
            return False
    
    return True

def main():
    """Main startup function."""
    
    print("🚀 CBRE AI Validation Agent - Startup Check")
    print("=" * 50)
    
    checks = [
        ("Environment Setup", setup_environment),
        ("Dependencies", check_dependencies),
        ("Configuration", check_configuration),
        ("API Key", check_api_key),
        ("Module Imports", test_imports)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        try:
            if check_func():
                passed += 1
            else:
                print(f"\n❌ {check_name} check failed")
        except Exception as e:
            print(f"\n❌ {check_name} check failed with error: {e}")
    
    print(f"\n📊 Results: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 All checks passed! The system is ready to use.")
        print("\nNext steps:")
        print("  1. Command line: python main.py --help")
        print("  2. Web interface: streamlit run streamlit_app/app.py")
        print("  3. Test with sample files")
        return 0
    else:
        print(f"\n❌ {total - passed} checks failed. Please fix the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    input("\nPress Enter to continue...")
    sys.exit(exit_code)
