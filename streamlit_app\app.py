import streamlit as st
import pandas as pd
import json
import time
import traceback
from pathlib import Path
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
from typing import Dict, Any, List, Optional

# Add the parent directory to the path so we can import our modules
import sys
sys.path.append(str(Path(__file__).parent.parent))

from agent_chain import run_validation_chain
from agents.guardrail_agent.log_utils import get_agent_logger
from utils.file_handler import load_excel_as_dicts
from memory.session_context import session_context

# Configure Streamlit page
st.set_page_config(
    page_title="CBRE AI Validation Agent",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2c3e50;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .status-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin: 1rem 0;
    }
    .status-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin: 1rem 0;
    }
    .status-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin: 1rem 0;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """Main Streamlit application."""

    # Initialize session state
    if 'validation_results' not in st.session_state:
        st.session_state.validation_results = None
    if 'processing_logs' not in st.session_state:
        st.session_state.processing_logs = []
    if 'uploaded_files' not in st.session_state:
        st.session_state.uploaded_files = {}

    # Main header
    st.markdown('<div class="main-header">🔍 CBRE AI Validation Agent</div>', unsafe_allow_html=True)
    st.markdown("---")

    # Sidebar for navigation and configuration
    with st.sidebar:
        st.header("Navigation")
        page = st.selectbox(
            "Select Page",
            ["File Upload & Validation", "Results Dashboard", "Agent Logs", "System Status"]
        )

        st.header("Configuration")

        # API Configuration
        with st.expander("OpenAI Configuration"):
            api_key_input = st.text_input(
                "OpenAI API Key",
                type="password",
                help="Enter your OpenAI API key"
            )
            if api_key_input:
                import os
                os.environ['OPENAI_API_KEY'] = api_key_input
                st.success("API Key configured!")

        # Processing Options
        with st.expander("Processing Options"):
            batch_size = st.slider("Batch Size", 10, 500, 100)
            enable_detailed_logging = st.checkbox("Enable Detailed Logging", True)
            strict_validation = st.checkbox("Strict Validation Mode", False)

    # Route to different pages
    if page == "File Upload & Validation":
        show_upload_validation_page()
    elif page == "Results Dashboard":
        show_results_dashboard()
    elif page == "Agent Logs":
        show_agent_logs()
    elif page == "System Status":
        show_system_status()

def show_upload_validation_page():
    """Display the file upload and validation page."""

    st.markdown('<div class="section-header">📁 File Upload & Validation</div>', unsafe_allow_html=True)

    # Create two columns for file uploads
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("1. Upload Stored Procedure")
        sp_file = st.file_uploader(
            "Choose SQL file",
            type=['sql', 'txt'],
            help="Upload the stored procedure file containing validation rules"
        )

        if sp_file:
            st.session_state.uploaded_files['sp_file'] = sp_file

            # Preview the SQL content
            sql_content = sp_file.read().decode('utf-8')
            sp_file.seek(0)  # Reset file pointer

            with st.expander("Preview SQL Content"):
                st.code(sql_content[:1000] + "..." if len(sql_content) > 1000 else sql_content, language='sql')

    with col2:
        st.subheader("2. Upload Data File")
        data_file = st.file_uploader(
            "Choose Excel file",
            type=['xlsx', 'xls', 'csv'],
            help="Upload the data file to validate"
        )

        if data_file:
            st.session_state.uploaded_files['data_file'] = data_file

            # Preview the data
            try:
                if data_file.name.endswith('.csv'):
                    df = pd.read_csv(data_file)
                else:
                    df = pd.read_excel(data_file)
                data_file.seek(0)  # Reset file pointer

                with st.expander("Preview Data"):
                    st.dataframe(df.head(10))
                    st.info(f"File contains {len(df)} rows and {len(df.columns)} columns")

            except Exception as e:
                st.error(f"Error reading file: {e}")

    # Validation section
    st.markdown('<div class="section-header">🚀 Run Validation</div>', unsafe_allow_html=True)

    if st.session_state.uploaded_files.get('sp_file') and st.session_state.uploaded_files.get('data_file'):

        # Validation options
        col1, col2, col3 = st.columns(3)

        with col1:
            validate_button = st.button("🔍 Start Validation", type="primary", use_container_width=True)

        with col2:
            if st.button("📊 Quick Analysis", use_container_width=True):
                show_quick_analysis()

        with col3:
            if st.button("🧹 Clear Results", use_container_width=True):
                st.session_state.validation_results = None
                st.session_state.processing_logs = []
                st.rerun()

        if validate_button:
            run_validation_process()
    else:
        st.info("Please upload both a stored procedure file and a data file to begin validation.")

    # Display validation results if available
    if st.session_state.validation_results:
        display_validation_results()

def run_validation_process():
    """Run the complete validation process."""

    try:
        # Create temporary files
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False) as sp_temp:
            sp_content = st.session_state.uploaded_files['sp_file'].read().decode('utf-8')
            sp_temp.write(sp_content)
            sp_temp_path = sp_temp.name

        with tempfile.NamedTemporaryFile(mode='wb', suffix='.xlsx', delete=False) as data_temp:
            data_temp.write(st.session_state.uploaded_files['data_file'].read())
            data_temp_path = data_temp.name

        # Reset file pointers
        st.session_state.uploaded_files['sp_file'].seek(0)
        st.session_state.uploaded_files['data_file'].seek(0)

        # Show progress
        progress_bar = st.progress(0)
        status_text = st.empty()

        # Create a container for real-time logs
        log_container = st.container()

        with log_container:
            st.subheader("🔄 Processing Logs")
            log_placeholder = st.empty()

        # Start validation with progress tracking
        status_text.text("🔍 Parsing stored procedure...")
        progress_bar.progress(20)

        start_time = time.time()

        # Run the validation chain
        try:
            results = run_validation_chain(sp_temp_path, data_temp_path)

            progress_bar.progress(100)
            processing_time = time.time() - start_time

            # Store results
            st.session_state.validation_results = {
                **results,
                'processing_time': processing_time,
                'timestamp': datetime.now().isoformat(),
                'sp_filename': st.session_state.uploaded_files['sp_file'].name,
                'data_filename': st.session_state.uploaded_files['data_file'].name
            }

            status_text.text(f"✅ Validation completed in {processing_time:.2f} seconds!")

            # Show success message
            st.success("🎉 Validation process completed successfully!")

        except Exception as e:
            st.error(f"❌ Validation failed: {str(e)}")
            st.exception(e)

        finally:
            # Clean up temporary files
            try:
                os.unlink(sp_temp_path)
                os.unlink(data_temp_path)
            except:
                pass

    except Exception as e:
        st.error(f"Error setting up validation: {str(e)}")
        st.exception(e)

def display_validation_results():
    """Display the validation results."""

    st.markdown('<div class="section-header">📊 Validation Results</div>', unsafe_allow_html=True)

    results = st.session_state.validation_results

    # Summary metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "Processing Time",
            f"{results.get('processing_time', 0):.2f}s",
            help="Total time taken for validation"
        )

    with col2:
        status = results.get('status', 'unknown')
        st.metric(
            "Status",
            status.upper(),
            help="Overall validation status"
        )

    with col3:
        violations = len(results.get('violations', []))
        st.metric(
            "Violations",
            violations,
            help="Number of guardrail violations detected"
        )

    with col4:
        if 'file' in results:
            st.metric(
                "Output File",
                "Generated",
                help="Validation results saved to file"
            )

    # Detailed results
    tab1, tab2, tab3, tab4 = st.tabs(["📋 Summary", "⚠️ Violations", "📈 Analytics", "📄 Raw Data"])

    with tab1:
        show_results_summary(results)

    with tab2:
        show_violations_details(results)

    with tab3:
        show_validation_analytics(results)

    with tab4:
        show_raw_results(results)

def show_results_summary(results: Dict[str, Any]):
    """Show validation results summary."""

    # Processing information
    st.subheader("📋 Processing Summary")

    col1, col2 = st.columns(2)

    with col1:
        st.info(f"**Stored Procedure:** {results.get('sp_filename', 'Unknown')}")
        st.info(f"**Data File:** {results.get('data_filename', 'Unknown')}")
        st.info(f"**Processing Time:** {results.get('processing_time', 0):.2f} seconds")

    with col2:
        st.info(f"**Timestamp:** {results.get('timestamp', 'Unknown')}")
        st.info(f"**Status:** {results.get('status', 'Unknown').upper()}")
        st.info(f"**Violations:** {len(results.get('violations', []))}")

    # File information
    if 'file' in results:
        st.success(f"✅ **Output File Generated:** `{results['file']}`")

        # Provide download link if file exists
        try:
            output_path = Path(results['file'])
            if output_path.exists():
                with open(output_path, 'rb') as f:
                    st.download_button(
                        label="📥 Download Validation Results",
                        data=f.read(),
                        file_name=output_path.name,
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    )
        except Exception as e:
            st.warning(f"Could not prepare download: {e}")

def show_violations_details(results: Dict[str, Any]):
    """Show detailed violation information."""

    violations = results.get('violations', [])

    if not violations:
        st.success("🎉 No violations detected! All validations passed.")
        return

    st.subheader(f"⚠️ Violations Detected ({len(violations)})")

    # Group violations by severity
    violation_groups = {}
    for violation in violations:
        severity = violation.get('severity', 'unknown')
        if severity not in violation_groups:
            violation_groups[severity] = []
        violation_groups[severity].append(violation)

    # Display violations by severity
    severity_order = ['critical', 'high', 'medium', 'low', 'unknown']
    severity_colors = {
        'critical': '🔴',
        'high': '🟠',
        'medium': '🟡',
        'low': '🟢',
        'unknown': '⚪'
    }

    for severity in severity_order:
        if severity in violation_groups:
            st.markdown(f"### {severity_colors.get(severity, '⚪')} {severity.upper()} Severity ({len(violation_groups[severity])})")

            for i, violation in enumerate(violation_groups[severity]):
                with st.expander(f"{severity_colors.get(severity, '⚪')} {violation.get('policy', 'Unknown Policy')} - {violation.get('description', 'No description')[:100]}..."):

                    col1, col2 = st.columns(2)

                    with col1:
                        st.write("**Policy:**", violation.get('policy', 'Unknown'))
                        st.write("**Severity:**", violation.get('severity', 'Unknown'))
                        st.write("**Description:**", violation.get('description', 'No description available'))

                    with col2:
                        st.write("**Recommendation:**", violation.get('recommendation', 'No recommendation available'))
                        if 'affected_component' in violation:
                            st.write("**Affected Component:**", violation['affected_component'])

def show_validation_analytics(results: Dict[str, Any]):
    """Show validation analytics and visualizations."""

    st.subheader("📈 Validation Analytics")

    violations = results.get('violations', [])

    if violations:
        # Violations by severity chart
        severity_counts = {}
        for violation in violations:
            severity = violation.get('severity', 'unknown')
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        if severity_counts:
            fig_severity = px.pie(
                values=list(severity_counts.values()),
                names=list(severity_counts.keys()),
                title="Violations by Severity",
                color_discrete_map={
                    'critical': '#ff4444',
                    'high': '#ff8800',
                    'medium': '#ffcc00',
                    'low': '#44ff44',
                    'unknown': '#cccccc'
                }
            )
            st.plotly_chart(fig_severity, use_container_width=True)

        # Violations by policy chart
        policy_counts = {}
        for violation in violations:
            policy = violation.get('policy', 'Unknown')
            policy_counts[policy] = policy_counts.get(policy, 0) + 1

        if policy_counts:
            fig_policy = px.bar(
                x=list(policy_counts.keys()),
                y=list(policy_counts.values()),
                title="Violations by Policy",
                labels={'x': 'Policy', 'y': 'Count'}
            )
            st.plotly_chart(fig_policy, use_container_width=True)

    else:
        st.success("🎉 No violations to analyze - all validations passed!")

    # Processing time analysis
    processing_time = results.get('processing_time', 0)

    # Create a simple gauge chart for processing time
    fig_time = go.Figure(go.Indicator(
        mode = "gauge+number",
        value = processing_time,
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': "Processing Time (seconds)"},
        gauge = {
            'axis': {'range': [None, max(60, processing_time * 1.5)]},
            'bar': {'color': "darkblue"},
            'steps': [
                {'range': [0, 10], 'color': "lightgray"},
                {'range': [10, 30], 'color': "gray"},
                {'range': [30, 60], 'color': "orange"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 60
            }
        }
    ))

    st.plotly_chart(fig_time, use_container_width=True)

def show_raw_results(results: Dict[str, Any]):
    """Show raw validation results."""

    st.subheader("📄 Raw Results Data")

    # Display results as JSON
    st.json(results)

    # Provide option to download raw results
    results_json = json.dumps(results, indent=2, default=str)

    st.download_button(
        label="📥 Download Raw Results (JSON)",
        data=results_json,
        file_name=f"validation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        mime="application/json"
    )

def show_quick_analysis():
    """Show quick analysis of uploaded files."""

    st.subheader("📊 Quick File Analysis")

    # Analyze data file
    if 'data_file' in st.session_state.uploaded_files:
        data_file = st.session_state.uploaded_files['data_file']

        try:
            if data_file.name.endswith('.csv'):
                df = pd.read_csv(data_file)
            else:
                df = pd.read_excel(data_file)
            data_file.seek(0)  # Reset file pointer

            col1, col2 = st.columns(2)

            with col1:
                st.metric("Total Rows", len(df))
                st.metric("Total Columns", len(df.columns))

                # Check for NAV file specific columns
                nav_columns = ['UNIQUE_ID', 'FUND_ID', 'NAV', 'FUND_CODE']
                present_nav_cols = [col for col in nav_columns if col in df.columns]
                st.metric("NAV Columns Present", f"{len(present_nav_cols)}/{len(nav_columns)}")

            with col2:
                # Data quality metrics
                null_percentage = (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
                st.metric("Null Values %", f"{null_percentage:.1f}%")

                # Duplicate rows
                duplicate_rows = df.duplicated().sum()
                st.metric("Duplicate Rows", duplicate_rows)

                # Unique ID check
                if 'UNIQUE_ID' in df.columns:
                    unique_ids = df['UNIQUE_ID'].nunique()
                    st.metric("Unique IDs", f"{unique_ids}/{len(df)}")

            # Column analysis
            st.subheader("Column Analysis")

            column_info = []
            for col in df.columns:
                column_info.append({
                    'Column': col,
                    'Type': str(df[col].dtype),
                    'Null Count': df[col].isnull().sum(),
                    'Null %': f"{(df[col].isnull().sum() / len(df)) * 100:.1f}%",
                    'Unique Values': df[col].nunique()
                })

            column_df = pd.DataFrame(column_info)
            st.dataframe(column_df, use_container_width=True)

        except Exception as e:
            st.error(f"Error analyzing data file: {e}")

def show_results_dashboard():
    """Show the results dashboard page."""

    st.markdown('<div class="section-header">📊 Results Dashboard</div>', unsafe_allow_html=True)

    if not st.session_state.validation_results:
        st.info("No validation results available. Please run a validation first.")
        return

    # Display current results
    display_validation_results()

    # Historical results (if we had a database)
    st.subheader("📈 Historical Analysis")
    st.info("Historical analysis would be available with database integration.")

def show_agent_logs():
    """Show agent logs and activity."""

    st.markdown('<div class="section-header">📋 Agent Logs</div>', unsafe_allow_html=True)

    try:
        agent_logger = get_agent_logger()

        # Time range selector
        col1, col2 = st.columns(2)

        with col1:
            hours_back = st.selectbox("Time Range", [1, 6, 12, 24, 48, 168], index=3)

        with col2:
            agent_filter = st.selectbox(
                "Filter by Agent",
                ["All", "sp_parser_agent", "file_validation_agent", "data_validation_agent", "guardrail_agent"]
            )

        # Get logs
        if agent_filter == "All":
            # Get summary of all violations
            violation_summary = agent_logger.get_violation_summary(hours_back)

            st.subheader("📊 Violation Summary")

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Total Violations", violation_summary['total_violations'])

            with col2:
                st.metric("Active Violations", violation_summary['active_violations'])

            with col3:
                critical_count = violation_summary['by_severity'].get('critical', 0)
                st.metric("Critical Violations", critical_count)

            with col4:
                high_count = violation_summary['by_severity'].get('high', 0)
                st.metric("High Severity", high_count)

            # Violations by severity
            if violation_summary['by_severity']:
                fig = px.bar(
                    x=list(violation_summary['by_severity'].keys()),
                    y=list(violation_summary['by_severity'].values()),
                    title="Violations by Severity"
                )
                st.plotly_chart(fig, use_container_width=True)

            # Violations by policy
            if violation_summary['by_policy']:
                fig = px.pie(
                    values=list(violation_summary['by_policy'].values()),
                    names=list(violation_summary['by_policy'].keys()),
                    title="Violations by Policy"
                )
                st.plotly_chart(fig, use_container_width=True)

        else:
            # Get specific agent logs
            agent_logs = agent_logger.get_agent_logs(agent_filter, hours_back)

            st.subheader(f"📋 {agent_filter} Logs ({len(agent_logs)} entries)")

            if agent_logs:
                # Convert to DataFrame for display
                log_df = pd.DataFrame(agent_logs)

                # Format timestamp
                if 'timestamp' in log_df.columns:
                    log_df['timestamp'] = pd.to_datetime(log_df['timestamp']).dt.strftime('%Y-%m-%d %H:%M:%S')

                st.dataframe(log_df, use_container_width=True)
            else:
                st.info(f"No logs found for {agent_filter} in the last {hours_back} hours.")

    except Exception as e:
        st.error(f"Error loading agent logs: {e}")
        st.exception(e)

def show_system_status():
    """Show system status and health."""

    st.markdown('<div class="section-header">🔧 System Status</div>', unsafe_allow_html=True)

    # API Status
    st.subheader("🔌 API Status")

    col1, col2 = st.columns(2)

    with col1:
        # Check OpenAI API key
        import os
        api_key = os.getenv('OPENAI_API_KEY')
        if api_key:
            st.success("✅ OpenAI API Key: Configured")
        else:
            st.error("❌ OpenAI API Key: Not configured")

    with col2:
        # Check file system
        try:
            Path("logs").mkdir(exist_ok=True)
            Path("data/output").mkdir(parents=True, exist_ok=True)
            st.success("✅ File System: Accessible")
        except Exception as e:
            st.error(f"❌ File System: Error - {e}")

    # System Information
    st.subheader("💻 System Information")

    import platform
    import sys

    system_info = {
        "Python Version": sys.version,
        "Platform": platform.platform(),
        "Processor": platform.processor(),
        "Architecture": platform.architecture()[0]
    }

    for key, value in system_info.items():
        st.info(f"**{key}:** {value}")

    # Dependencies Status
    st.subheader("📦 Dependencies")

    dependencies = [
        "streamlit", "pandas", "openai", "langchain", "pydantic",
        "sqlglot", "plotly", "openpyxl"
    ]

    for dep in dependencies:
        try:
            __import__(dep)
            st.success(f"✅ {dep}: Installed")
        except ImportError:
            st.error(f"❌ {dep}: Not installed")

# Run the app
if __name__ == "__main__":
    main()