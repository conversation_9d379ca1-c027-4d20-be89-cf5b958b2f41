#!/usr/bin/env python3
"""
Encoding Fix Script for CBRE Validation Agent

This script fixes encoding issues in configuration files and ensures
they are properly saved with UTF-8 encoding.
"""

import os
import sys
from pathlib import Path

def fix_file_encoding(file_path: str):
    """Fix encoding issues in a file by reading and re-saving with UTF-8."""
    
    file_path = Path(file_path)
    
    if not file_path.exists():
        print(f"File not found: {file_path}")
        return False
    
    print(f"Fixing encoding for: {file_path}")
    
    # Try different encodings to read the file
    encodings_to_try = ['utf-8', 'utf-8-sig', 'cp1252', 'latin1', 'ascii']
    
    content = None
    original_encoding = None
    
    for encoding in encodings_to_try:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
                original_encoding = encoding
                print(f"  Successfully read with {encoding} encoding")
                break
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"  Error reading with {encoding}: {e}")
            continue
    
    if content is None:
        print(f"  ERROR: Could not read file with any encoding")
        return False
    
    # Write back with UTF-8 encoding
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  Successfully saved with UTF-8 encoding")
        return True
    except Exception as e:
        print(f"  ERROR: Could not save file: {e}")
        return False

def main():
    """Main function to fix encoding issues."""
    
    print("CBRE Validation Agent - Encoding Fix Script")
    print("=" * 50)
    
    # Files that might have encoding issues
    files_to_fix = [
        "config/settings.yaml",
        "config/schema_definitions.json",
        "docs/guardrail_policies.md",
        "docs/README.md"
    ]
    
    success_count = 0
    total_count = 0
    
    for file_path in files_to_fix:
        total_count += 1
        if fix_file_encoding(file_path):
            success_count += 1
        print()
    
    print(f"Encoding fix completed: {success_count}/{total_count} files processed successfully")
    
    # Test if the configuration can now be loaded
    print("\nTesting configuration loading...")
    try:
        import yaml
        
        with open("config/settings.yaml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("✅ Configuration file loads successfully!")
        print(f"   Found {len(config)} top-level configuration sections")
        
    except Exception as e:
        print(f"❌ Configuration file still has issues: {e}")
        return 1
    
    print("\n🎉 All encoding issues have been resolved!")
    print("You can now run the validation agent without encoding errors.")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
