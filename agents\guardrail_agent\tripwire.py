def detect_tripwires(agent_output: dict, column_template: dict) -> list:
    violations = []
    allowed_columns = set(column_template.get("columns", []))

    # Scan for hallucinated columns
    if "rules" in agent_output:
        for rule in agent_output["rules"]:
            for col in rule.get("columns", []):
                if col not in allowed_columns:
                    violations.append(f"Column '{col}' is not in schema.")

    # Add fallback tripwire checks (e.g., empty output, logic anomalies)
    if not agent_output.get("rules"):
        violations.append("Agent output contains no rules. Potential fallback failure.")

    return violations