from pathlib import Path
import json

def build_guardrail_prompt(agent_output: dict, column_template: dict, rules_json: dict) -> str:
    template_path = Path("config/prompt_templates/guardrail_prompt.txt")
    if not template_path.exists():
        raise FileNotFoundError("Prompt template not found at:", template_path)

    template = template_path.read_text()
    prompt = template.replace("{agent_output_json}", json.dumps(agent_output, indent=2))
    prompt = prompt.replace("{column_template_json}", json.dumps(column_template, indent=2))
    prompt = prompt.replace("{rules_json}", json.dumps(rules_json, indent=2))
    return prompt