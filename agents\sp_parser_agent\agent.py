import json
from .prompt_builder import build_sp_parser_prompt
from .schema import RuleExtractionOutput
from utils.openai_client import call_openai
from sqlglot import parse_one, ParseError


def parse_stored_procedure(sql_text: str, procedure_name: str) -> RuleExtractionOutput:
    try:
        # Attempt structural parse
        _ = parse_one(sql_text)
        print("SQLGlot parsed successfully. Proceeding to LLM for confirmation.")
    except ParseError:
        print("SQLGlot failed. Using fallback LLM interpretation.")

    prompt = build_sp_parser_prompt(sql_text)
    response = call_openai(prompt)

    # Assume response is valid JSON (GuardrailAgent will validate externally)
    output = RuleExtractionOutput.parse_raw(response)
    return output