#!/usr/bin/env python3
"""
Simple test script to verify basic functionality without complex imports.
"""

import os
import sys
from pathlib import Path

def test_basic_setup():
    """Test basic setup without importing complex modules."""
    
    print("🧪 CBRE Validation Agent - Simple Test")
    print("=" * 40)
    
    # Test 1: Python version
    print(f"Python version: {sys.version}")
    if sys.version_info >= (3, 10):
        print("✅ Python version OK")
    else:
        print("⚠️  Python 3.10+ recommended")
    
    # Test 2: Required packages
    print("\nTesting package imports...")
    packages = ['yaml', 'pandas', 'openai', 'streamlit', 'pydantic']
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - run: pip install {package}")
    
    # Test 3: Directory structure
    print("\nChecking directories...")
    dirs = ['config', 'agents', 'utils', 'logs', 'data']
    for directory in dirs:
        if Path(directory).exists():
            print(f"✅ {directory}/")
        else:
            print(f"❌ {directory}/ (missing)")
            Path(directory).mkdir(exist_ok=True)
            print(f"   Created {directory}/")
    
    # Test 4: Configuration files
    print("\nChecking configuration...")
    config_file = "config/settings.yaml"
    
    if Path(config_file).exists():
        try:
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            print(f"✅ {config_file} (valid)")
        except Exception as e:
            print(f"❌ {config_file} (error): {e}")
    else:
        print(f"❌ {config_file} (missing)")
    
    # Test 5: API Key
    print("\nChecking API key...")
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        if api_key.startswith('sk-'):
            print("✅ OpenAI API key configured")
        else:
            print("⚠️  API key format looks incorrect")
    else:
        print("❌ OpenAI API key not set")
        print("   Set with: set OPENAI_API_KEY=your-key")
    
    # Test 6: Simple YAML loading
    print("\nTesting YAML loading...")
    try:
        import yaml
        
        # Create a simple test YAML
        test_yaml = """
test:
  value: "hello"
  number: 42
"""
        
        # Test parsing
        data = yaml.safe_load(test_yaml)
        if data['test']['value'] == 'hello':
            print("✅ YAML parsing works")
        else:
            print("❌ YAML parsing failed")
            
    except Exception as e:
        print(f"❌ YAML test failed: {e}")
    
    print("\n" + "=" * 40)
    print("Test completed!")
    print("\nIf all tests pass, try:")
    print("  python main.py --help")
    print("  streamlit run streamlit_app/app.py")

if __name__ == "__main__":
    test_basic_setup()
    input("\nPress Enter to exit...")
