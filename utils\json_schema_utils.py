import jsonschema

def validate_rules_schema(rules_json: dict):
    schema = {
        "type": "object",
        "properties": {
            "procedure_name": {"type": "string"},
            "rules": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "columns": {"type": "array", "items": {"type": "string"}},
                        "rule_type": {"type": "string"},
                        "value": {},
                        "message": {"type": "string"}
                    },
                    "required": ["columns", "rule_type", "message"]
                }
            }
        },
        "required": ["procedure_name", "rules"]
    }
    jsonschema.validate(instance=rules_json, schema=schema)
