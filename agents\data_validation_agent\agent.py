from .prompt_builder import build_data_validation_prompt
from .schema import BusinessValidationResult
from utils.openai_client import call_openai


def validate_business_logic(row: dict, rules_json: dict, reference_data: dict, history: dict) -> BusinessValidationResult:
    prompt = build_data_validation_prompt(row, rules_json, reference_data, history)
    response = call_openai(prompt)
    result = BusinessValidationResult.parse_raw(response)
    return result
