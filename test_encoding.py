#!/usr/bin/env python3
"""
Test script to verify encoding issues are resolved.
"""

import sys
import os
import traceback

def test_yaml_loading():
    """Test if YAML configuration can be loaded without encoding errors."""

    print("Testing YAML configuration loading...")

    try:
        import yaml
        print("   ✅ PyYAML imported successfully")

        # Check if config file exists
        config_path = "config/settings.yaml"
        if not os.path.exists(config_path):
            print(f"   ❌ Config file not found: {config_path}")
            return False

        print(f"   ✅ Config file exists: {config_path}")

        # Test loading the configuration file
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        print("   ✅ YAML configuration loaded successfully!")
        print(f"   Found {len(config)} configuration sections:")
        for key in config.keys():
            print(f"     - {key}")

        return True

    except Exception as e:
        print(f"   ❌ YAML loading failed: {e}")
        print(f"   Full traceback: {traceback.format_exc()}")
        return False

def test_logging_config():
    """Test if logging configuration can be initialized."""

    print("\nTesting logging configuration...")

    try:
        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)
        print("   ✅ Logs directory created/verified")

        # Test basic logging setup without importing the full module
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        print("   ✅ Basic logging setup successful")

        # Now try to import our logging config
        sys.path.insert(0, '.')  # Add current directory to path
        from utils.logging_config import setup_logging
        print("   ✅ Logging config module imported")

        setup_logging()
        print("   ✅ Logging configuration initialized successfully!")
        return True

    except Exception as e:
        print(f"   ❌ Logging configuration failed: {e}")
        print(f"   Full traceback: {traceback.format_exc()}")
        return False

def test_openai_client():
    """Test if OpenAI client can be initialized."""

    print("\nTesting OpenAI client initialization...")

    try:
        # Test OpenAI import first
        import openai
        print("   ✅ OpenAI package imported successfully")

        # Test our client module
        from utils.openai_client import get_openai_client
        print("   ✅ OpenAI client module imported")

        # This will test config loading but won't fail if API key is missing
        try:
            client = get_openai_client()
            print("   ✅ OpenAI client configuration loaded successfully!")
            print("   Note: You still need to set OPENAI_API_KEY environment variable")
            return True
        except ValueError as ve:
            if "OPENAI_API_KEY" in str(ve):
                print("   ✅ OpenAI client config loaded (API key not set, which is expected)")
                return True
            else:
                raise ve

    except Exception as e:
        print(f"   ❌ OpenAI client initialization failed: {e}")
        print(f"   Full traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all tests."""
    
    print("CBRE Validation Agent - Encoding Test")
    print("=" * 40)
    
    tests = [
        test_yaml_loading,
        test_logging_config,
        test_openai_client
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\nTest Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The encoding issue has been resolved.")
        print("You can now run the validation agent:")
        print("  python main.py --help")
        print("  streamlit run streamlit_app/app.py")
        return 0
    else:
        print(f"\n❌ {total - passed} tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
