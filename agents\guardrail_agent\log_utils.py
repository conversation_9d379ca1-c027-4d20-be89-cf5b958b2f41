import json
from pathlib import Path
from datetime import datetime

LOG_PATH = Path("logs/guardrail_log.json")
LOG_PATH.parent.mkdir(parents=True, exist_ok=True)


def log_violation(entry: dict):
    log_entry = {
        "timestamp": datetime.utcnow().isoformat(),
        **entry
    }
    if LOG_PATH.exists():
        existing = json.loads(LOG_PATH.read_text())
    else:
        existing = []
    existing.append(log_entry)
    LOG_PATH.write_text(json.dumps(existing, indent=2))