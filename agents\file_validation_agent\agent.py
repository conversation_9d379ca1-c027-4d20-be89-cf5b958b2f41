import pandas as pd
import logging
import time
import traceback
from typing import Dict, Any, List, Optional
from .schema import ValidationResult
from .prompt_builder import build_file_validation_prompt
from utils.openai_client import get_openai_client
from utils.json_schema_utils import validate_json_structure
from agents.guardrail_agent.log_utils import get_agent_logger

logger = logging.getLogger(__name__)

def validate_row(row: dict, rules_json: dict, row_index: Optional[int] = None) -> ValidationResult:
    """
    AI-powered file validation agent that validates individual rows against extracted rules.

    Args:
        row: The data row to validate
        rules_json: The extracted validation rules
        row_index: Optional row index for logging

    Returns:
        ValidationResult with validation outcome

    Raises:
        RuntimeError: If validation fails due to system errors
        ValueError: If response validation fails
    """
    agent_logger = get_agent_logger()
    openai_client = get_openai_client()

    start_time = time.time()

    # Prepare activity details for logging
    activity_details = {
        "row_index": row_index,
        "rules_count": len(rules_json.get("rules", [])),
        "row_fields": list(row.keys()),
        "processing_time_ms": 0,
        "input_size": len(str(row)) + len(str(rules_json))
    }

    try:
        logger.debug(f"Starting file validation for row {row_index or 'unknown'}")

        # Build the validation prompt
        try:
            prompt = build_file_validation_prompt(row, rules_json)
        except Exception as e:
            error_msg = f"Failed to build validation prompt: {e}"
            logger.error(error_msg)
            agent_logger.log_error("file_validation_agent", "prompt_building_error", error_msg,
                                 {"row_index": row_index, "row_data": row}, traceback.format_exc())
            raise RuntimeError(error_msg)

        # Call OpenAI API with enhanced error handling
        try:
            system_message = (
                "You are an expert data validation agent. Carefully evaluate each validation rule "
                "against the provided data row. Be precise in your analysis and provide clear "
                "explanations for any violations found."
            )

            response = openai_client.call_openai(
                prompt=prompt,
                system_message=system_message,
                agent_name="file_validation_agent"
            )

            activity_details["output_size"] = len(response)

        except Exception as e:
            error_msg = f"OpenAI API call failed for file validation: {e}"
            logger.error(error_msg)
            agent_logger.log_error("file_validation_agent", "api_call_error", error_msg,
                                 {"row_index": row_index}, traceback.format_exc())
            raise RuntimeError(error_msg)

        # Validate and parse the JSON response
        try:
            validation_result = validate_json_structure(response, "validation_result_schema")

            if not validation_result["parsed"]:
                error_msg = f"Failed to parse JSON response: {validation_result['errors']}"
                logger.error(error_msg)
                agent_logger.log_error("file_validation_agent", "json_parsing_error", error_msg,
                                     {"row_index": row_index, "response_preview": response[:500]})
                raise ValueError(error_msg)

            if not validation_result["valid"]:
                logger.warning(f"Response validation failed: {validation_result['errors']}")
                # Continue processing but log the warning
                agent_logger.log_error("file_validation_agent", "schema_validation_warning",
                                     f"Response schema validation failed: {validation_result['errors']}",
                                     {"row_index": row_index})

            # Parse into Pydantic model
            result = ValidationResult.parse_raw(response)

            # Additional validation logic
            _enhance_validation_result(result, row, rules_json, agent_logger, row_index)

            # Calculate processing time
            processing_time = time.time() - start_time
            activity_details["processing_time_ms"] = int(processing_time * 1000)
            activity_details["validation_result"] = "error" if result.error else "success"

            # Log successful completion
            agent_logger.log_agent_activity(
                "file_validation_agent",
                "validate_row",
                activity_details,
                "success"
            )

            logger.debug(f"File validation completed for row {row_index or 'unknown'} "
                        f"in {processing_time:.3f}s - Result: {'FAIL' if result.error else 'PASS'}")

            return result

        except Exception as e:
            error_msg = f"Failed to validate or parse response: {e}"
            logger.error(error_msg)
            agent_logger.log_error("file_validation_agent", "response_validation_error", error_msg,
                                 {"row_index": row_index, "response": response[:1000]},
                                 traceback.format_exc())
            raise ValueError(error_msg)

    except Exception as e:
        # Calculate processing time even for failures
        processing_time = time.time() - start_time
        activity_details["processing_time_ms"] = int(processing_time * 1000)

        # Log failed activity
        agent_logger.log_agent_activity(
            "file_validation_agent",
            "validate_row",
            activity_details,
            "error"
        )

        logger.error(f"File validation failed for row {row_index or 'unknown'}: {e}")
        raise

def validate_batch(rows: List[dict], rules_json: dict, batch_size: int = 100) -> List[ValidationResult]:
    """
    Validate multiple rows in batches for improved performance.

    Args:
        rows: List of data rows to validate
        rules_json: The extracted validation rules
        batch_size: Number of rows to process in each batch

    Returns:
        List of ValidationResult objects
    """
    agent_logger = get_agent_logger()
    results = []

    logger.info(f"Starting batch validation of {len(rows)} rows with batch size {batch_size}")

    for i in range(0, len(rows), batch_size):
        batch = rows[i:i + batch_size]
        batch_start_time = time.time()

        logger.info(f"Processing batch {i//batch_size + 1}/{(len(rows) + batch_size - 1)//batch_size}")

        batch_results = []
        for j, row in enumerate(batch):
            try:
                result = validate_row(row, rules_json, i + j)
                batch_results.append(result)
            except Exception as e:
                logger.error(f"Failed to validate row {i + j}: {e}")
                # Create a default error result
                error_result = ValidationResult(
                    error=True,
                    error_message=f"System error during validation: {str(e)}"
                )
                batch_results.append(error_result)

        results.extend(batch_results)

        batch_time = time.time() - batch_start_time
        logger.info(f"Batch completed in {batch_time:.2f}s - "
                   f"Errors: {sum(1 for r in batch_results if r.error)}/{len(batch_results)}")

    # Log batch summary
    total_errors = sum(1 for r in results if r.error)
    error_rate = total_errors / len(results) if results else 0

    agent_logger.log_agent_activity(
        "file_validation_agent",
        "validate_batch",
        {
            "total_rows": len(rows),
            "batch_size": batch_size,
            "total_errors": total_errors,
            "error_rate": error_rate,
            "rules_count": len(rules_json.get("rules", []))
        },
        "success"
    )

    logger.info(f"Batch validation completed. Total errors: {total_errors}/{len(results)} ({error_rate:.1%})")

    return results

def _enhance_validation_result(result: ValidationResult, row: dict, rules_json: dict,
                              agent_logger, row_index: Optional[int]) -> None:
    """
    Enhance validation result with additional checks and context.

    Args:
        result: The validation result to enhance
        row: The original data row
        rules_json: The validation rules
        agent_logger: Logger instance
        row_index: Row index for context
    """
    # Add additional context or warnings if needed
    warnings = []

    # Check for missing critical fields
    critical_fields = ["UNIQUE_ID", "FUND_ID", "NAV"]  # NAV file specific
    missing_critical = [field for field in critical_fields if field not in row or row[field] is None]

    if missing_critical:
        warnings.append(f"Missing critical fields: {', '.join(missing_critical)}")

    # Check for unusual data patterns
    if "NAV" in row:
        try:
            nav_value = float(row["NAV"]) if row["NAV"] is not None else None
            if nav_value is not None:
                if nav_value > 1_000_000_000:  # $1B threshold
                    warnings.append(f"Unusually high NAV value: ${nav_value:,.2f}")
                elif nav_value < 0:
                    warnings.append(f"Negative NAV value: ${nav_value:,.2f}")
        except (ValueError, TypeError):
            warnings.append("NAV value is not numeric")

    # Log warnings if any
    if warnings:
        agent_logger.log_error("file_validation_agent", "data_quality_warning",
                             f"Data quality warnings for row {row_index}",
                             {"row_index": row_index, "warnings": warnings, "row_data": row})
