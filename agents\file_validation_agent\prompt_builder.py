from pathlib import Path
import json

def build_file_validation_prompt(row_data: dict, rules_json: dict) -> str:
    template_path = Path("config/prompt_templates/file_validator_prompt.txt")
    if not template_path.exists():
        raise FileNotFoundError("Prompt template not found at:", template_path)

    template = template_path.read_text()
    prompt = template.replace("{rules_json}", json.dumps(rules_json, indent=2))
    prompt = prompt.replace("{row_data}", json.dumps(row_data, indent=2))
    return prompt