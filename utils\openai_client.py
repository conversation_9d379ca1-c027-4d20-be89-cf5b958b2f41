import openai
import time

openai.api_key = "your-api-key"  # Replace this with your key or env var


def call_openai(prompt: str, model="gpt-4", max_retries=3) -> str:
    for attempt in range(max_retries):
        try:
            response = openai.ChatCompletion.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.0
            )
            return response["choices"][0]["message"]["content"]
        except Exception as e:
            print(f"Retry {attempt + 1} failed: {e}")
            time.sleep(1)
    raise RuntimeError("OpenAI API call failed after retries")
