from .prompt_builder import build_guardrail_prompt
from .schema import GuardrailResult
from .tripwire import detect_tripwires
from .log_utils import log_violation
from utils.openai_client import call_openai


def enforce_guardrails(agent_output: dict, column_template: dict, rules_json: dict) -> GuardrailResult:
    # Tripwire detection (pre-validation logic)
    violations = detect_tripwires(agent_output, column_template)

    if violations:
        log_violation({"status": "blocked", "violations": violations})
        return GuardrailResult(status="blocked", violations=violations, action="review_required")

    prompt = build_guardrail_prompt(agent_output, column_template, rules_json)
    response = call_openai(prompt)
    result = GuardrailResult.parse_raw(response)

    if result.status == "blocked":
        log_violation(result.dict())

    return result