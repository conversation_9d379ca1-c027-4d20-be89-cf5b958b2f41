import logging
import time
import traceback
from typing import Dict, Any, Optional, List
from .prompt_builder import build_guardrail_prompt
from .schema import GuardrailResult
from .tripwire import get_tripwire_detector
from .log_utils import get_agent_logger
from utils.openai_client import get_openai_client
from utils.json_schema_utils import validate_json_structure

logger = logging.getLogger(__name__)

def enforce_guardrails(agent_output: dict, column_template: dict, rules_json: dict,
                      context: Optional[Dict[str, Any]] = None) -> GuardrailResult:
    """
    AI-powered guardrail enforcement agent that provides comprehensive safety
    and integrity validation for the entire validation system.

    Args:
        agent_output: Output from previous agents to validate
        column_template: Expected column schema
        rules_json: Original extracted rules for comparison
        context: Additional context for validation

    Returns:
        GuardrailResult with comprehensive safety assessment

    Raises:
        RuntimeError: If guardrail enforcement fails due to system errors
    """
    agent_logger = get_agent_logger()
    openai_client = get_openai_client()
    tripwire_detector = get_tripwire_detector()

    start_time = time.time()

    # Prepare activity details for logging
    activity_details = {
        "agent_output_size": len(str(agent_output)),
        "column_count": len(column_template.get("columns", [])),
        "rules_count": len(rules_json.get("rules", [])),
        "context_provided": bool(context),
        "processing_time_ms": 0,
        "tripwire_violations": 0,
        "ai_violations": 0
    }

    try:
        logger.info("Starting comprehensive guardrail enforcement")

        # Phase 1: Tripwire detection (pre-validation logic)
        logger.debug("Phase 1: Running tripwire detection")
        tripwire_violations = tripwire_detector.detect_tripwires(agent_output, column_template, context)
        activity_details["tripwire_violations"] = len(tripwire_violations)

        # Check for critical violations that require immediate blocking
        critical_violations = [v for v in tripwire_violations if v.get("severity") == "critical"]

        if critical_violations:
            logger.warning(f"Critical tripwire violations detected: {len(critical_violations)}")

            # Log all violations
            for violation in tripwire_violations:
                violation_id = agent_logger.log_violation(violation, "guardrail_agent")
                logger.warning(f"Tripwire violation logged: {violation_id}")

            # Create blocked result
            result = GuardrailResult(
                status="blocked",
                violations=tripwire_violations,
                action="block_processing",
                risk_score=1.0  # Maximum risk for critical violations
            )

            # Log the blocking decision
            activity_details["final_status"] = "blocked"
            activity_details["block_reason"] = "critical_tripwire_violations"

            processing_time = time.time() - start_time
            activity_details["processing_time_ms"] = int(processing_time * 1000)

            agent_logger.log_agent_activity(
                "guardrail_agent",
                "enforce_guardrails",
                activity_details,
                "blocked"
            )

            logger.warning(f"Guardrail enforcement BLOCKED due to critical violations in {processing_time:.3f}s")
            return result

        # Phase 2: AI-powered comprehensive analysis
        logger.debug("Phase 2: Running AI-powered guardrail analysis")

        try:
            # Build the guardrail prompt with enhanced context
            enhanced_context = _build_enhanced_context(agent_output, column_template, rules_json,
                                                     tripwire_violations, context)
            prompt = build_guardrail_prompt(enhanced_context["agent_output"],
                                          enhanced_context["column_template"],
                                          enhanced_context["rules_json"])
        except Exception as e:
            error_msg = f"Failed to build guardrail prompt: {e}"
            logger.error(error_msg)
            agent_logger.log_error("guardrail_agent", "prompt_building_error", error_msg,
                                 {"agent_output_preview": str(agent_output)[:500]}, traceback.format_exc())
            raise RuntimeError(error_msg)

        # Call OpenAI API for comprehensive analysis
        try:
            system_message = (
                "You are the final AI Guardrail Agent responsible for system safety and integrity. "
                "Perform comprehensive analysis of all agent outputs, enforce safety policies, "
                "detect anomalies, and make critical decisions about system operation. "
                "Your decisions directly impact business operations and regulatory compliance."
            )

            response = openai_client.call_openai(
                prompt=prompt,
                system_message=system_message,
                agent_name="guardrail_agent",
                temperature=0.0  # Maximum determinism for safety decisions
            )

        except Exception as e:
            error_msg = f"OpenAI API call failed for guardrail enforcement: {e}"
            logger.error(error_msg)
            agent_logger.log_error("guardrail_agent", "api_call_error", error_msg, {}, traceback.format_exc())

            # Fail-safe: block processing if AI analysis fails
            result = GuardrailResult(
                status="blocked",
                violations=[{
                    "policy": "GP-007",
                    "severity": "high",
                    "description": f"Guardrail AI analysis failed: {str(e)}",
                    "recommendation": "Manual review required due to system error"
                }],
                action="review_required",
                risk_score=0.8
            )

            processing_time = time.time() - start_time
            activity_details["processing_time_ms"] = int(processing_time * 1000)
            activity_details["final_status"] = "blocked"
            activity_details["block_reason"] = "ai_analysis_failure"

            agent_logger.log_agent_activity(
                "guardrail_agent",
                "enforce_guardrails",
                activity_details,
                "error"
            )

            return result

        # Phase 3: Validate and parse AI response
        try:
            validation_result = validate_json_structure(response, "guardrail_result_schema")

            if not validation_result["parsed"]:
                error_msg = f"Failed to parse guardrail JSON response: {validation_result['errors']}"
                logger.error(error_msg)

                # Fail-safe: block processing if response is invalid
                result = GuardrailResult(
                    status="blocked",
                    violations=[{
                        "policy": "GP-007",
                        "severity": "high",
                        "description": "Guardrail response parsing failed",
                        "recommendation": "Manual review required due to response format error"
                    }],
                    action="review_required",
                    risk_score=0.8
                )

                agent_logger.log_error("guardrail_agent", "response_parsing_error", error_msg,
                                     {"response_preview": response[:500]})

                processing_time = time.time() - start_time
                activity_details["processing_time_ms"] = int(processing_time * 1000)
                activity_details["final_status"] = "blocked"
                activity_details["block_reason"] = "response_parsing_failure"

                agent_logger.log_agent_activity(
                    "guardrail_agent",
                    "enforce_guardrails",
                    activity_details,
                    "error"
                )

                return result

            # Parse into Pydantic model
            result = GuardrailResult.parse_raw(response)

            # Combine tripwire violations with AI-detected violations
            all_violations = tripwire_violations + result.violations
            result.violations = all_violations
            activity_details["ai_violations"] = len(result.violations) - len(tripwire_violations)

            # Enhanced risk assessment
            _enhance_guardrail_result(result, agent_output, tripwire_violations, context, agent_logger)

        except Exception as e:
            error_msg = f"Failed to process guardrail response: {e}"
            logger.error(error_msg)
            agent_logger.log_error("guardrail_agent", "response_processing_error", error_msg,
                                 {"response": response[:1000]}, traceback.format_exc())

            # Fail-safe: block processing
            result = GuardrailResult(
                status="blocked",
                violations=[{
                    "policy": "GP-007",
                    "severity": "high",
                    "description": f"Guardrail response processing failed: {str(e)}",
                    "recommendation": "Manual review required due to processing error"
                }],
                action="review_required",
                risk_score=0.8
            )

        # Phase 4: Final decision and logging
        processing_time = time.time() - start_time
        activity_details["processing_time_ms"] = int(processing_time * 1000)
        activity_details["final_status"] = result.status
        activity_details["risk_score"] = getattr(result, 'risk_score', None)
        activity_details["total_violations"] = len(result.violations)

        # Log all violations
        if result.violations:
            for violation in result.violations:
                if violation not in tripwire_violations:  # Don't double-log tripwire violations
                    violation_id = agent_logger.log_violation(violation, "guardrail_agent")
                    logger.info(f"Guardrail violation logged: {violation_id}")

        # Log final decision
        agent_logger.log_agent_activity(
            "guardrail_agent",
            "enforce_guardrails",
            activity_details,
            "success" if result.status == "approved" else "blocked"
        )

        logger.info(f"Guardrail enforcement completed in {processing_time:.3f}s - "
                   f"Status: {result.status.upper()}, Violations: {len(result.violations)}")

        return result

    except Exception as e:
        # Calculate processing time even for failures
        processing_time = time.time() - start_time
        activity_details["processing_time_ms"] = int(processing_time * 1000)

        # Log failed activity
        agent_logger.log_agent_activity(
            "guardrail_agent",
            "enforce_guardrails",
            activity_details,
            "error"
        )

        logger.error(f"Guardrail enforcement failed: {e}")
        raise

def _build_enhanced_context(agent_output: dict, column_template: dict, rules_json: dict,
                           tripwire_violations: List[Dict], context: Optional[Dict]) -> Dict[str, Any]:
    """
    Build enhanced context for AI guardrail analysis.

    Args:
        agent_output: Original agent output
        column_template: Column template
        rules_json: Rules JSON
        tripwire_violations: Detected tripwire violations
        context: Additional context

    Returns:
        Enhanced context dictionary
    """
    enhanced = {
        "agent_output": agent_output.copy(),
        "column_template": column_template.copy(),
        "rules_json": rules_json.copy()
    }

    # Add tripwire analysis results
    enhanced["tripwire_analysis"] = {
        "violations_detected": len(tripwire_violations),
        "critical_violations": len([v for v in tripwire_violations if v.get("severity") == "critical"]),
        "violation_summary": [v.get("description", "") for v in tripwire_violations[:5]]  # Top 5
    }

    # Add processing context
    if context:
        enhanced["processing_context"] = context

    return enhanced

def _enhance_guardrail_result(result: GuardrailResult, agent_output: dict,
                             tripwire_violations: List[Dict], context: Optional[Dict],
                             agent_logger) -> None:
    """
    Enhance guardrail result with additional analysis and risk assessment.

    Args:
        result: The guardrail result to enhance
        agent_output: Original agent output
        tripwire_violations: Tripwire violations
        context: Additional context
        agent_logger: Logger instance
    """
    # Calculate enhanced risk score
    base_risk = getattr(result, 'risk_score', 0.0)

    # Adjust risk based on violation severity
    severity_weights = {"critical": 0.4, "high": 0.3, "medium": 0.2, "low": 0.1}
    violation_risk = 0.0

    for violation in result.violations:
        severity = violation.get("severity", "medium")
        violation_risk += severity_weights.get(severity, 0.2)

    # Cap violation risk at 1.0
    violation_risk = min(violation_risk, 1.0)

    # Combine risks (weighted average)
    enhanced_risk = (base_risk * 0.6) + (violation_risk * 0.4)

    # Update result with enhanced risk score
    if hasattr(result, 'risk_score'):
        result.risk_score = enhanced_risk

    # Log risk assessment details
    agent_logger.log_agent_activity(
        "guardrail_agent",
        "risk_assessment",
        {
            "base_risk_score": base_risk,
            "violation_risk_score": violation_risk,
            "enhanced_risk_score": enhanced_risk,
            "total_violations": len(result.violations),
            "tripwire_violations": len(tripwire_violations)
        },
        "success"
    )